# Sidebar Auto-Expand Dropdown Test Guide

## Overview
This document provides manual testing instructions for the enhanced sidebar component that automatically expands dropdown menus when the sidebar is opened and a sub-item is currently active.

## Implementation Summary

### Enhancement Made
Modified the `toggleSidebar()` function to automatically expand Knowledge Base or Settings dropdowns when:
1. Sidebar transitions from collapsed to expanded state (`wasMinimized = true` → `isMinimized = false`)
2. A sub-item from Knowledge Base or Settings is currently active
3. Uses existing `activeKnowledgeBaseItem` and `activeSettingsItem` reactive variables

### Code Changes
```javascript
function toggleSidebar() {
    const wasMinimized = isMinimized;
    isMinimized = !isMinimized;
    
    // whenever we toggle…
    if (isMinimized) {
        clearSidebarAutoMinimize();
    } else {
        scheduleSidebarAutoMinimize();
        
        // Auto-expand dropdowns when expanding sidebar if sub-items are active
        if (wasMinimized) {
            // Check if Knowledge Base sub-item is active and auto-expand
            if (activeKnowledgeBaseItem) {
                isKnowledgeBaseOpen = true;
            }
            
            // Check if Settings sub-item is active and auto-expand
            if (activeSettingsItem) {
                isSettingsOpen = true;
            }
        }
    }
}
```

## Manual Testing Instructions

### Test Case 1: Knowledge Base Auto-Expansion (Upload Files)
1. **Setup**: Login to the application
2. **Navigate**: Go to `/knowledge` (Upload Files page)
3. **Initial State**: Verify sidebar is expanded and Knowledge Base dropdown shows "Upload Files" as active
4. **Collapse**: Click the sidebar toggle button (chevron icon)
5. **Verify Collapsed**: Confirm sidebar is collapsed and active sub-item is visible
6. **Expand**: Click the sidebar toggle button again
7. **Expected Result**:
   - Sidebar expands to full width
   - Knowledge Base dropdown automatically opens (`isKnowledgeBaseOpen = true`)
   - "Upload Files" sub-item is visible and highlighted as active
   - All Knowledge Base sub-items are visible in the dropdown

### Test Case 2: Knowledge Base Auto-Expansion (Testing)
1. **Navigate**: Go to `/llm_testing` (Testing page)
2. **Collapse**: Click sidebar toggle button
3. **Expand**: Click sidebar toggle button again
4. **Expected Result**:
   - Sidebar expands
   - Knowledge Base dropdown automatically opens
   - "Testing" sub-item is visible and highlighted as active

### Test Case 3: Settings Auto-Expansion (Business)
1. **Navigate**: Go to `/settings/business` (Business Settings)
2. **Collapse**: Click sidebar toggle button
3. **Expand**: Click sidebar toggle button again
4. **Expected Result**:
   - Sidebar expands
   - Settings dropdown automatically opens (`isSettingsOpen = true`)
   - "Business" sub-item is visible and highlighted as active
   - All accessible Settings sub-items are visible

### Test Case 4: Settings Auto-Expansion (Team Management)
1. **Navigate**: Go to `/settings/team` (Team Management)
2. **Collapse**: Click sidebar toggle button
3. **Expand**: Click sidebar toggle button again
4. **Expected Result**:
   - Sidebar expands
   - Settings dropdown automatically opens
   - "Team Management" sub-item is visible and highlighted as active

### Test Case 5: Settings Auto-Expansion (Account)
1. **Navigate**: Go to `/settings/account` (Account Settings)
2. **Collapse**: Click sidebar toggle button
3. **Expand**: Click sidebar toggle button again
4. **Expected Result**:
   - Sidebar expands
   - Settings dropdown automatically opens
   - "Account" sub-item is visible and highlighted as active

### Test Case 6: No Auto-Expansion (Main Pages)
1. **Navigate**: Go to `/chat_center` (main page without sub-items)
2. **Collapse**: Click sidebar toggle button
3. **Expand**: Click sidebar toggle button again
4. **Expected Result**:
   - Sidebar expands
   - NO dropdowns automatically open
   - Only main navigation items visible
   - Clean, standard expanded sidebar state

### Test Case 7: Manual Dropdown Toggle Still Works
1. **Navigate**: Go to any main page (e.g., `/chat_center`)
2. **Ensure**: Sidebar is expanded
3. **Manual Toggle**: Click on "Knowledge Base" or "Settings" dropdown trigger
4. **Expected Result**:
   - Dropdown opens/closes as before
   - Manual toggle functionality preserved
   - No interference from auto-expansion logic

### Test Case 8: Multiple Collapse/Expand Cycles
1. **Navigate**: Go to `/knowledge` page
2. **Perform**: Multiple collapse/expand cycles (5-6 times)
3. **Expected Result**:
   - Each expansion automatically opens Knowledge Base dropdown
   - Consistent behavior across multiple cycles
   - No performance issues or glitches

### Test Case 9: Role-Based Settings Auto-Expansion
1. **Test with different user roles** (Admin, Supervisor, Agent)
2. **Navigate**: To accessible settings pages for each role
3. **Collapse/Expand**: Sidebar
4. **Expected Result**:
   - Auto-expansion only occurs for settings pages accessible to current role
   - Proper role-based filtering maintained

## Verification Checklist

### Functional Requirements
- [ ] Knowledge Base dropdown auto-expands when expanding from `/knowledge` page
- [ ] Knowledge Base dropdown auto-expands when expanding from `/llm_testing` page
- [ ] Settings dropdown auto-expands when expanding from `/settings/business` page
- [ ] Settings dropdown auto-expands when expanding from `/settings/team` page
- [ ] Settings dropdown auto-expands when expanding from `/settings/account` page
- [ ] No auto-expansion occurs on main pages without active sub-items
- [ ] Manual dropdown toggle functionality preserved
- [ ] Auto-expansion only occurs when transitioning from collapsed to expanded

### Technical Requirements
- [ ] Uses existing `activeKnowledgeBaseItem` reactive variable
- [ ] Uses existing `activeSettingsItem` reactive variable
- [ ] Only triggers on `wasMinimized = true` → `isMinimized = false` transition
- [ ] Maintains all existing sidebar functionality
- [ ] No performance impact or memory leaks

### User Experience
- [ ] Smooth transitions and animations
- [ ] Intuitive behavior that matches user expectations
- [ ] No visual glitches or layout shifts
- [ ] Consistent behavior across different pages
- [ ] Works well with existing auto-minimize functionality

## Edge Cases to Test

### Navigation During Collapsed State
1. **Start**: On `/knowledge` page with collapsed sidebar
2. **Navigate**: To `/settings/account` while sidebar remains collapsed
3. **Expand**: Sidebar
4. **Expected**: Settings dropdown opens (not Knowledge Base)

### Rapid Toggle
1. **Perform**: Very rapid collapse/expand clicks
2. **Expected**: Stable behavior, no race conditions

### Browser Refresh
1. **Navigate**: To settings page
2. **Collapse**: Sidebar
3. **Refresh**: Browser
4. **Expand**: Sidebar
5. **Expected**: Auto-expansion works after refresh

## Success Criteria
✅ All test cases pass
✅ No regression in existing functionality
✅ Smooth, intuitive user experience
✅ Consistent behavior across all supported pages
✅ Proper role-based access control maintained
